/**
 * Tests for Arrow Key Navigation in ConversationPane and SummaryPane
 */

import React from 'react';
import { render, fireEvent, screen, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import ConversationPane from '../../src/frontend/components/ConversationPane.jsx';
import SummaryPane from '../../src/frontend/components/SummaryPane.jsx';

// Mock messageNavigator utilities
jest.mock('../../src/frontend/utils/messageNavigator.js', () => ({
  buildRealIndex: jest.fn(() => new Map()),
  scrollTo: jest.fn(),
  clearHighlight: jest.fn(),
  buildLazyLoadIndex: jest.fn(() => null),
  DEFAULT_PAGE_SIZE: 10,
  HIGHLIGHT_CLASS: 'highlighted'
}));

describe('Arrow Key Navigation', () => {
  describe('ConversationPane Arrow Navigation', () => {
    const mockMessages = [
      { message_id: 'msg1', content: 'First message', sender: 'user' },
      { message_id: 'msg2', content: 'Second message', sender: 'assistant' },
      { message_id: 'msg3', content: 'Third message', sender: 'user' }
    ];

    test('ArrowDown moves focus to next message', async () => {
      const { container } = render(
        <ConversationPane messages={mockMessages} onSend={jest.fn()} />
      );

      const messagesContainer = container.querySelector('.messages');
      const firstMessage = container.querySelector('[data-message-id="msg1"]');
      const secondMessage = container.querySelector('[data-message-id="msg2"]');

      // Focus the messages container
      act(() => {
        messagesContainer.focus();
      });

      // Simulate ArrowDown key
      fireEvent.keyDown(messagesContainer, { key: 'ArrowDown' });

      // Should focus the first message
      expect(firstMessage).toHaveAttribute('tabIndex', '0');
      
      // Focus first message and press ArrowDown again
      act(() => {
        firstMessage.focus();
      });
      
      fireEvent.keyDown(messagesContainer, { key: 'ArrowDown' });
      
      // Should move to second message
      expect(secondMessage).toHaveAttribute('tabIndex', '0');
    });

    test('ArrowUp moves focus to previous message', async () => {
      const { container } = render(
        <ConversationPane messages={mockMessages} onSend={jest.fn()} />
      );

      const messagesContainer = container.querySelector('.messages');
      const secondMessage = container.querySelector('[data-message-id="msg2"]');
      const firstMessage = container.querySelector('[data-message-id="msg1"]');

      // Focus second message
      act(() => {
        secondMessage.focus();
      });

      // Simulate ArrowUp key
      fireEvent.keyDown(messagesContainer, { key: 'ArrowUp' });

      // Should move to first message
      expect(firstMessage).toHaveAttribute('tabIndex', '0');
    });

    test('Arrow navigation ignores keys when focus is in input', () => {
      const { container } = render(
        <ConversationPane messages={mockMessages} onSend={jest.fn()} />
      );

      // The input in ConversationPane was changed to a <textarea> — support both selectors
      const input = container.querySelector('textarea') || container.querySelector('input');
      const messagesContainer = container.querySelector('.messages');

      // Focus the input
      act(() => {
        input.focus();
      });

      // Simulate ArrowDown while input is focused
      fireEvent.keyDown(input, { key: 'ArrowDown' });

      // Should not affect message navigation
      expect(document.activeElement).toBe(input);
    });

    test('Arrow navigation handles boundary conditions', () => {
      const { container } = render(
        <ConversationPane messages={mockMessages} onSend={jest.fn()} />
      );

      const messagesContainer = container.querySelector('.messages');

      // Test ArrowUp at beginning (should stay at first)
      fireEvent.keyDown(messagesContainer, { key: 'ArrowUp' });
      
      // Test ArrowDown at end (should stay at last)
      fireEvent.keyDown(messagesContainer, { key: 'ArrowDown' });
      fireEvent.keyDown(messagesContainer, { key: 'ArrowDown' });
      fireEvent.keyDown(messagesContainer, { key: 'ArrowDown' });
      fireEvent.keyDown(messagesContainer, { key: 'ArrowDown' }); // Should clamp to last

      // Should not throw errors
      expect(true).toBe(true);
    });
  });

  describe('SummaryPane Arrow Navigation', () => {
    const mockSummaries = [
      { summary_id: 'sum1', text: 'First summary', label: 'Summary 1' },
      { summary_id: 'sum2', text: 'Second summary', label: 'Summary 2' },
      { summary_id: 'sum3', text: 'Third summary', label: 'Summary 3' }
    ];

    test('ArrowDown moves focus to next summary', async () => {
      const { container } = render(
        <SummaryPane summaries={mockSummaries} onSelect={jest.fn()} />
      );

      const summaryContainer = container.querySelector('.summary-pane');
      const firstSummary = container.querySelector('[data-summary-id="sum1"]');
      const secondSummary = container.querySelector('[data-summary-id="sum2"]');

      // Focus the summary container
      act(() => {
        summaryContainer.focus();
      });

      // Simulate ArrowDown key
      fireEvent.keyDown(summaryContainer, { key: 'ArrowDown' });

      // Should focus the first summary
      expect(firstSummary).toHaveAttribute('tabIndex', '0');
    });

    test('ArrowUp moves focus to previous summary', async () => {
      const { container } = render(
        <SummaryPane summaries={mockSummaries} onSelect={jest.fn()} />
      );

      const summaryContainer = container.querySelector('.summary-pane');
      const secondSummary = container.querySelector('[data-summary-id="sum2"]');

      // Focus second summary first
      act(() => {
        secondSummary.focus();
      });

      // Simulate ArrowUp key
      fireEvent.keyDown(summaryContainer, { key: 'ArrowUp' });

      // Should move to first summary
      const firstSummary = container.querySelector('[data-summary-id="sum1"]');
      expect(firstSummary).toHaveAttribute('tabIndex', '0');
    });

    test('Arrow navigation calls onSelect when Enter is pressed', () => {
      const mockOnSelect = jest.fn();
      const { container } = render(
        <SummaryPane summaries={mockSummaries} onSelect={mockOnSelect} />
      );

      const firstSummary = container.querySelector('[data-summary-id="sum1"]');

      // Focus and press Enter
      act(() => {
        firstSummary.focus();
      });

      fireEvent.keyDown(firstSummary, { key: 'Enter' });

      expect(mockOnSelect).toHaveBeenCalledWith(mockSummaries[0]);
    });

    test('Arrow navigation ignores keys when focus is in input', () => {
      // Create a container with an input to test the ignore logic
      const TestComponent = () => (
        <div>
          <input data-testid="test-input" />
          <SummaryPane summaries={mockSummaries} onSelect={jest.fn()} />
        </div>
      );

      const { container } = render(<TestComponent />);
      const input = container.querySelector('[data-testid="test-input"]');
      const summaryContainer = container.querySelector('.summary-pane');

      // Focus the input
      act(() => {
        input.focus();
      });

      // Simulate ArrowDown while input is focused
      fireEvent.keyDown(input, { key: 'ArrowDown' });

      // Should not affect summary navigation
      expect(document.activeElement).toBe(input);
    });
  });

  describe('Accessibility Features', () => {
    test('Messages have proper ARIA attributes', () => {
      const mockMessages = [
        { message_id: 'msg1', content: 'Test message', sender: 'user' }
      ];

      const { container } = render(
        <ConversationPane messages={mockMessages} onSend={jest.fn()} />
      );

      const message = container.querySelector('[data-message-id="msg1"]');
      expect(message).toHaveAttribute('role', 'article');
      expect(message).toHaveAttribute('tabIndex', '0');
      expect(message).toHaveAttribute('aria-current', 'false');
    });

    test('Summaries have proper ARIA attributes', () => {
      const mockSummaries = [
        { summary_id: 'sum1', text: 'Test summary', label: 'Summary 1' }
      ];

      const { container } = render(
        <SummaryPane summaries={mockSummaries} onSelect={jest.fn()} />
      );

      const summary = container.querySelector('[data-summary-id="sum1"]');
      expect(summary).toHaveAttribute('role', 'listitem');
      expect(summary).toHaveAttribute('tabIndex', '0');
      expect(summary).toHaveAttribute('aria-current', 'false');
    });
  });
});
