/**
 * Integration Tests for Keyboard Interactions
 *
 * Tests the 4 main keyboard interactions:
 * 1. Send (Ctrl/Cmd+Enter)
 * 2. Newline (Shift+Enter)
 * 3. Esc clear highlights
 * 4. Arrow navigation
 */

import React from 'react';
import { render, fireEvent, screen, act, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ConversationPane from '../../src/frontend/components/ConversationPane.jsx';
import SummaryPane from '../../src/frontend/components/SummaryPane.jsx';
import App from '../../src/frontend/components/App.jsx';

// Mock messageNavigator utilities
jest.mock('../../src/frontend/utils/messageNavigator.js', () => ({
  buildRealIndex: jest.fn(() => new Map()),
  scrollTo: jest.fn(),
  clearHighlight: jest.fn(),
  buildLazyLoadIndex: jest.fn(() => null),
  DEFAULT_PAGE_SIZE: 10,
  HIGHLIGHT_CLASS: 'highlighted'
}));

// Mock fetch for App component
global.fetch = jest.fn();

describe('Keyboard Interactions Integration Tests', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock successful API responses for App component
    global.fetch.mockImplementation((url) => {
      if (url.includes('/conversation')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            results: [
              { message_id: 'msg1', content: 'Hello', sender: 'user' },
              { message_id: 'msg2', content: 'Hi there!', sender: 'assistant' }
            ]
          })
        });
      }
      if (url.includes('/summaries')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            results: [
              { summary_id: 'sum1', text: 'First summary', label: 'Summary 1' },
              { summary_id: 'sum2', text: 'Second summary', label: 'Summary 2' }
            ]
          })
        });
      }
      return Promise.resolve({ ok: true, json: () => Promise.resolve({}) });
    });
  });

  describe('1. Send Message (Ctrl/Cmd+Enter)', () => {
    test('sends message when Ctrl+Enter is pressed in ConversationPane', async () => {
      const mockSend = jest.fn().mockResolvedValue({});

      render(<ConversationPane messages={[]} onSend={mockSend} />);

      const input = screen.getByPlaceholderText(/输入消息/i);

      await act(async () => {
        fireEvent.change(input, { target: { value: 'Test message' } });
        fireEvent.keyDown(input, { key: 'Enter', ctrlKey: true });
        await Promise.resolve(); // Allow async handlers to complete
      });

      expect(mockSend).toHaveBeenCalledWith('Test message');
      expect(input.value).toBe(''); // Input should be cleared after send
    });

    test('sends message when Cmd+Enter is pressed (macOS)', async () => {
      const mockSend = jest.fn().mockResolvedValue({});

      render(<ConversationPane messages={[]} onSend={mockSend} />);

      const input = screen.getByPlaceholderText(/输入消息/i);

      await act(async () => {
        fireEvent.change(input, { target: { value: 'Test message' } });
        fireEvent.keyDown(input, { key: 'Enter', metaKey: true }); // macOS Cmd key
        await Promise.resolve();
      });

      expect(mockSend).toHaveBeenCalledWith('Test message');
    });

    test('does not send empty messages', async () => {
      const mockSend = jest.fn();

      render(<ConversationPane messages={[]} onSend={mockSend} />);

      const input = screen.getByPlaceholderText(/输入消息/i);

      await act(async () => {
        fireEvent.keyDown(input, { key: 'Enter', ctrlKey: true });
        await Promise.resolve();
      });

      expect(mockSend).not.toHaveBeenCalled();
    });
  });

  describe('2. Newline Insertion (Shift+Enter)', () => {
    test('handles Shift+Enter without sending message', async () => {
      const mockSend = jest.fn();
      render(<ConversationPane messages={[]} onSend={mockSend} />);

      const input = screen.getByPlaceholderText(/输入消息/i);

      await act(async () => {
        fireEvent.change(input, { target: { value: 'Line 1' } });
        fireEvent.keyDown(input, { key: 'Enter', shiftKey: true });
        await Promise.resolve();
      });

      // Should not send message when Shift+Enter is pressed
      expect(mockSend).not.toHaveBeenCalled();
      // Input should still contain text (may have newline added)
      expect(input.value).toContain('Line 1');
    });

    test('Shift+Enter does not trigger form submission', async () => {
      const mockSend = jest.fn();
      render(<ConversationPane messages={[]} onSend={mockSend} />);

      const input = screen.getByPlaceholderText(/输入消息/i);

      await act(async () => {
        fireEvent.change(input, { target: { value: 'Test text' } });
        fireEvent.keyDown(input, { key: 'Enter', shiftKey: true });
        await Promise.resolve();
      });

      // Should not trigger send
      expect(mockSend).not.toHaveBeenCalled();
    });
  });

  describe('3. Escape Key - Clear Highlights', () => {
    test('Escape key handler is set up in ConversationPane', async () => {
      const mockMessages = [
        { message_id: 'msg1', content: 'Test message', sender: 'user' }
      ];

      render(<ConversationPane messages={mockMessages} onSend={jest.fn()} />);

      // Test that Escape key doesn't throw errors
      await act(async () => {
        fireEvent.keyDown(window, { key: 'Escape' });
      });

      // Should complete without errors (basic smoke test)
      expect(true).toBe(true);
    });

    test('Escape key handler is set up in SummaryPane', async () => {
      const mockSummaries = [
        { summary_id: 'sum1', text: 'Test summary', label: 'Summary 1' }
      ];

      render(<SummaryPane summaries={mockSummaries} onSelect={jest.fn()} />);

      // Test that Escape key doesn't throw errors
      await act(async () => {
        fireEvent.keyDown(window, { key: 'Escape' });
      });

      // Should complete without errors (basic smoke test)
      expect(true).toBe(true);
    });

    test('ignores Escape when focus is in input field', async () => {
      const mockMessages = [
        { message_id: 'msg1', content: 'Test message', sender: 'user' }
      ];

      render(<ConversationPane messages={mockMessages} onSend={jest.fn()} />);

      const input = screen.getByPlaceholderText(/输入消息/i);

      await act(async () => {
        input.focus();
        fireEvent.keyDown(input, { key: 'Escape' });
      });

      // Should not clear highlights when input is focused
      const { clearHighlight } = require('../../src/frontend/utils/messageNavigator.js');
      expect(clearHighlight).not.toHaveBeenCalled();
    });
  });

  describe('4. Arrow Navigation', () => {
    test('navigates between messages with Arrow keys', async () => {
      const mockMessages = [
        { message_id: 'msg1', content: 'First message', sender: 'user' },
        { message_id: 'msg2', content: 'Second message', sender: 'assistant' },
        { message_id: 'msg3', content: 'Third message', sender: 'user' }
      ];

      const { container } = render(
        <ConversationPane messages={mockMessages} onSend={jest.fn()} />
      );

      const messagesContainer = container.querySelector('.messages');

      await act(async () => {
        // Focus the messages container
        messagesContainer.focus();

        // Navigate down
        fireEvent.keyDown(messagesContainer, { key: 'ArrowDown' });
      });

      // Should focus first message
      const firstMessage = container.querySelector('[data-message-id="msg1"]');
      expect(firstMessage).toHaveAttribute('tabIndex', '0');
    });

    test('navigates between summaries with Arrow keys', async () => {
      const mockSummaries = [
        { summary_id: 'sum1', text: 'First summary', label: 'Summary 1' },
        { summary_id: 'sum2', text: 'Second summary', label: 'Summary 2' }
      ];

      const { container } = render(
        <SummaryPane summaries={mockSummaries} onSelect={jest.fn()} />
      );

      const summaryContainer = container.querySelector('.summary-pane');

      await act(async () => {
        summaryContainer.focus();
        fireEvent.keyDown(summaryContainer, { key: 'ArrowDown' });
      });

      // Should focus first summary
      const firstSummary = container.querySelector('[data-summary-id="sum1"]');
      expect(firstSummary).toHaveAttribute('tabIndex', '0');
    });

    test('handles boundary conditions in navigation', async () => {
      const mockMessages = [
        { message_id: 'msg1', content: 'Only message', sender: 'user' }
      ];

      const { container } = render(
        <ConversationPane messages={mockMessages} onSend={jest.fn()} />
      );

      const messagesContainer = container.querySelector('.messages');

      await act(async () => {
        messagesContainer.focus();

        // Try to navigate up from first item (should stay at first)
        fireEvent.keyDown(messagesContainer, { key: 'ArrowUp' });

        // Try to navigate down from last item (should stay at last)
        fireEvent.keyDown(messagesContainer, { key: 'ArrowDown' });
        fireEvent.keyDown(messagesContainer, { key: 'ArrowDown' });
      });

      // Should not throw errors and handle gracefully
      expect(true).toBe(true);
    });

    test('ignores arrow keys when focus is in input field', async () => {
      const mockMessages = [
        { message_id: 'msg1', content: 'Test message', sender: 'user' }
      ];

      const { container } = render(
        <ConversationPane messages={mockMessages} onSend={jest.fn()} />
      );

      const input = screen.getByPlaceholderText(/输入消息/i);

      await act(async () => {
        input.focus();
        fireEvent.keyDown(input, { key: 'ArrowDown' });
      });

      // Focus should remain on input, not move to messages
      expect(document.activeElement).toBe(input);
    });
  });

  describe('5. Full App Integration', () => {
    test('all keyboard interactions work together in full app', async () => {
      const { container } = render(<App />);

      // Wait for app to load
      await waitFor(() => {
        expect(container.querySelector('.conversation-pane')).toBeInTheDocument();
        expect(container.querySelector('.summary-pane')).toBeInTheDocument();
      });

      // Test that all components are rendered and interactive
      // ConversationPane input may be <textarea> or <input> depending on implementation — support both
      const input = container.querySelector('textarea[placeholder*="输入消息"]') || container.querySelector('input[placeholder*="输入消息"]');
      const messagesContainer = container.querySelector('.messages');
      const summaryContainer = container.querySelector('.summary-pane');

      // Check if elements exist (they might not be rendered yet due to loading)
      if (input) {
        expect(input).toBeInTheDocument();
      }
      if (messagesContainer) {
        expect(messagesContainer).toBeInTheDocument();
      }
      if (summaryContainer) {
        expect(summaryContainer).toBeInTheDocument();
      }

      // Test basic keyboard interaction doesn't throw errors
      await act(async () => {
        if (input) {
          fireEvent.change(input, { target: { value: 'Test' } });
          fireEvent.keyDown(input, { key: 'Enter', shiftKey: true });
        }

        if (messagesContainer) {
          fireEvent.keyDown(messagesContainer, { key: 'ArrowDown' });
        }

        fireEvent.keyDown(window, { key: 'Escape' });
      });

      // Should complete without errors
      expect(true).toBe(true);
    });
  });
});