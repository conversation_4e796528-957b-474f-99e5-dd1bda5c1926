/**
 * App.test.js
 *
 * Basic integration tests for App layout and key UI pieces.
 * Uses @testing-library/react + Jest.
 */

import React from 'react';
import { render, screen, waitFor, fireEvent, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import App from '../../src/frontend/components/App.jsx';

// Ensure App's fetch calls succeed in Node environment by mocking global.fetch
beforeEach(() => {
  global.fetch = jest.fn((url, options) => {
    if (url.includes('/conversation')) {
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          results: [
            { message_id: 'msg1', content: 'Hello', sender: 'user' }
          ]
        })
      });
    }
    if (url.includes('/summaries')) {
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          results: [
            { summary_id: 'sum1', text: 'First summary', label: 'Summary 1' }
          ]
        })
      });
    }
    // Mock POST /api/topics/:topic_id/messages
    if (url.includes('/messages') && options?.method === 'POST') {
      const body = JSON.parse(options.body);
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          message_id: `msg_${Date.now()}`,
          role: body.role,
          content: body.content,
          created_at: new Date().toISOString()
        })
      });
    }
    return Promise.resolve({ ok: true, json: () => Promise.resolve({}) });
  });
});

test('renders summary pane header', async () => {
  const { container } = render(<App />);

  // Wait for the app to finish loading and render the summary pane
  await waitFor(() => {
    if (!container.querySelector('.summary-pane')) throw new Error('summary not ready');
    return true;
  });

  // SummaryPane contains the header "对话摘要"
  expect(screen.getByText(/对话摘要/)).toBeInTheDocument();
});

test('renders conversation pane area', async () => {
  const { container } = render(<App />);

  // Wait for conversation pane to render
  await waitFor(() => {
    if (!container.querySelector('.conversation-pane')) throw new Error('conversation not ready');
    return true;
  });

  // ConversationPane shows the mocked message
  expect(screen.getByText(/Hello/)).toBeInTheDocument();
});

test('sends message to backend API when user submits', async () => {
  const { container } = render(<App />);

  // Wait for conversation pane to render
  await waitFor(() => {
    if (!container.querySelector('.conversation-pane')) throw new Error('conversation not ready');
    return true;
  });

  // Find the message input and send button
  const messageInput = screen.getByPlaceholderText(/输入消息/);
  const sendButton = screen.getByText('发送');

  // Type a message and send it
  await act(async () => {
    fireEvent.change(messageInput, { target: { value: 'Test message from user' } });
    fireEvent.click(sendButton);

    // Wait for the API call to complete
    await new Promise(resolve => setTimeout(resolve, 100));
  });

  // Verify that fetch was called with correct parameters
  expect(global.fetch).toHaveBeenCalledWith(
    '/api/topics/1/messages',
    expect.objectContaining({
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        role: 'user',
        content: 'Test message from user'
      })
    })
  );

  // Verify that the message appears in the conversation
  await waitFor(() => {
    expect(screen.getByText('Test message from user')).toBeInTheDocument();
  });
});