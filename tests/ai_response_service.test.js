const {
  generateAIResponse,
  buildConversationContext,
  AI_ERROR_TYPES,
  classifyError,
  getFallbackResponse,
  calculateBackoffDelay
} = require('../src/services/ai_response_service');

// Mock dependencies
jest.mock('../src/lib/openai_client');
jest.mock('../src/services/retrieval_service');
jest.mock('../src/lib/telemetry');

const { getOpenAIClient } = require('../src/lib/openai_client');
const { searchContext } = require('../src/services/retrieval_service');
const telemetry = require('../src/lib/telemetry');

describe('AI Response Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    telemetry.emit.mockImplementation(() => {});
  });

  describe('buildConversationContext', () => {
    test('should build context from recent messages', async () => {
      const mockResults = [
        {
          source_type: 'conversation',
          role: 'user',
          content: 'Hello',
          created_at: '2024-01-01T10:00:00Z'
        },
        {
          source_type: 'conversation', 
          role: 'assistant',
          content: 'Hi there!',
          created_at: '2024-01-01T10:01:00Z'
        }
      ];

      searchContext.mockResolvedValue({ results: mockResults });

      const result = await buildConversationContext(1, 10);

      expect(result.messages).toHaveLength(2);
      expect(result.messages[0]).toEqual({ role: 'user', content: 'Hello' });
      expect(result.messages[1]).toEqual({ role: 'assistant', content: 'Hi there!' });
      expect(result.summary).toContain('2 条消息');
      expect(telemetry.emit).toHaveBeenCalledWith('ai.context_built', expect.any(Object));
    });

    test('should handle empty conversation history', async () => {
      searchContext.mockResolvedValue({ results: [] });

      const result = await buildConversationContext(1, 10);

      expect(result.messages).toHaveLength(0);
      expect(result.summary).toContain('新的对话');
    });

    test('should handle retrieval service errors', async () => {
      searchContext.mockRejectedValue(new Error('Retrieval failed'));

      const result = await buildConversationContext(1, 10);

      expect(result.messages).toHaveLength(0);
      expect(result.summary).toContain('无法获取对话历史');
      expect(telemetry.emit).toHaveBeenCalledWith('ai.context_build_failed', expect.any(Object));
    });

    test('should limit messages to maxMessages parameter', async () => {
      const mockResults = Array.from({ length: 20 }, (_, i) => ({
        source_type: 'conversation',
        role: i % 2 === 0 ? 'user' : 'assistant',
        content: `Message ${i}`,
        created_at: `2024-01-01T10:${i.toString().padStart(2, '0')}:00Z`
      }));

      searchContext.mockResolvedValue({ results: mockResults });

      const result = await buildConversationContext(1, 5);

      expect(result.messages).toHaveLength(5);
      // Should get the most recent 5 messages
      expect(result.messages[4].content).toBe('Message 19');
    });
  });

  describe('classifyError', () => {
    test('should classify rate limit errors', () => {
      const error = { status: 429, message: 'Rate limit exceeded' };
      expect(classifyError(error)).toBe(AI_ERROR_TYPES.RATE_LIMIT);
    });

    test('should classify timeout errors', () => {
      const error = { message: 'Request timeout', code: 'ECONNABORTED' };
      expect(classifyError(error)).toBe(AI_ERROR_TYPES.TIMEOUT);
    });

    test('should classify context too long errors', () => {
      const error = { message: 'Context window too long' };
      expect(classifyError(error)).toBe(AI_ERROR_TYPES.CONTEXT_TOO_LONG);
    });

    test('should classify network errors', () => {
      const error = { message: 'Network connection failed' };
      expect(classifyError(error)).toBe(AI_ERROR_TYPES.NETWORK_ERROR);
    });

    test('should default to API error for unknown errors', () => {
      const error = { message: 'Unknown error' };
      expect(classifyError(error)).toBe(AI_ERROR_TYPES.API_ERROR);
    });
  });

  describe('getFallbackResponse', () => {
    test('should return appropriate fallback for rate limit', () => {
      const response = getFallbackResponse(AI_ERROR_TYPES.RATE_LIMIT);
      expect(response).toContain('请求较多');
    });

    test('should return appropriate fallback for timeout', () => {
      const response = getFallbackResponse(AI_ERROR_TYPES.TIMEOUT);
      expect(response).toContain('超时');
    });

    test('should return random fallback for unknown error types', () => {
      const response = getFallbackResponse('unknown');
      expect(typeof response).toBe('string');
      expect(response.length).toBeGreaterThan(0);
    });
  });

  describe('calculateBackoffDelay', () => {
    test('should calculate exponential backoff with jitter', () => {
      const delay0 = calculateBackoffDelay(0);
      const delay1 = calculateBackoffDelay(1);
      const delay2 = calculateBackoffDelay(2);

      expect(delay0).toBeGreaterThanOrEqual(1000);
      expect(delay0).toBeLessThan(3000);
      expect(delay1).toBeGreaterThanOrEqual(2000);
      expect(delay1).toBeLessThan(4000);
      expect(delay2).toBeGreaterThanOrEqual(4000);
      expect(delay2).toBeLessThan(6000);
    });

    test('should cap delay at maximum', () => {
      const delay = calculateBackoffDelay(10);
      expect(delay).toBeLessThanOrEqual(11000); // 10s max + 1s jitter
    });
  });

  describe('generateAIResponse', () => {
    let mockOpenAIClient;

    beforeEach(() => {
      mockOpenAIClient = {
        chat: {
          completions: {
            create: jest.fn()
          }
        }
      };
      getOpenAIClient.mockReturnValue(mockOpenAIClient);
      searchContext.mockResolvedValue({ results: [] });
    });

    test('should generate successful AI response', async () => {
      const mockResponse = {
        choices: [{ message: { content: 'This is an AI response' } }],
        usage: { total_tokens: 50 }
      };
      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockResponse);

      const result = await generateAIResponse(1, 'Hello');

      expect(result.content).toBe('This is an AI response');
      expect(result.metadata.tokens_used).toBe(50);
      expect(result.metadata.attempt).toBe(1);
      expect(telemetry.emit).toHaveBeenCalledWith('ai.response_generated', expect.any(Object));
    });

    test('should include conversation context when requested', async () => {
      const mockResponse = {
        choices: [{ message: { content: 'Response with context' } }],
        usage: { total_tokens: 75 }
      };
      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockResponse);
      
      const mockContextResults = [
        { source_type: 'conversation', role: 'user', content: 'Previous message' }
      ];
      searchContext.mockResolvedValue({ results: mockContextResults });

      const result = await generateAIResponse(1, 'Hello', { includeContext: true });

      expect(result.content).toBe('Response with context');
      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledWith(
        expect.objectContaining({
          messages: expect.arrayContaining([
            expect.objectContaining({ role: 'system' }),
            expect.objectContaining({ role: 'user', content: 'Previous message' }),
            expect.objectContaining({ role: 'user', content: 'Hello' })
          ])
        })
      );
    });

    test('should retry on rate limit errors', async () => {
      const rateLimitError = { status: 429, message: 'Rate limit exceeded' };
      const mockResponse = {
        choices: [{ message: { content: 'Success after retry' } }],
        usage: { total_tokens: 30 }
      };

      mockOpenAIClient.chat.completions.create
        .mockRejectedValueOnce(rateLimitError)
        .mockResolvedValueOnce(mockResponse);

      const result = await generateAIResponse(1, 'Hello');

      expect(result.content).toBe('Success after retry');
      expect(result.metadata.attempt).toBe(2);
      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledTimes(2);
    });

    test('should use fallback response after max retries', async () => {
      const apiError = { message: 'API Error' };
      mockOpenAIClient.chat.completions.create.mockRejectedValue(apiError);

      const result = await generateAIResponse(1, 'Hello');

      expect(result.metadata.fallback).toBe(true);
      expect(result.metadata.attempts).toBe(3);
      expect(result.content).toContain('技术问题');
      expect(telemetry.emit).toHaveBeenCalledWith('ai.response_fallback', expect.any(Object));
    });

    test('should handle timeout errors', async () => {
      mockOpenAIClient.chat.completions.create.mockImplementation(
        () => new Promise((resolve, reject) => {
          setTimeout(() => reject(new Error('Request timeout')), 100);
        })
      );

      const result = await generateAIResponse(1, 'Hello');

      expect(result.metadata.fallback).toBe(true);
      expect(result.content).toContain('超时');
    });

    test('should handle invalid response format', async () => {
      const invalidResponse = { choices: [] };
      mockOpenAIClient.chat.completions.create.mockResolvedValue(invalidResponse);

      const result = await generateAIResponse(1, 'Hello');

      expect(result.metadata.fallback).toBe(true);
      expect(typeof result.content).toBe('string');
    });

    test('should use custom client when provided', async () => {
      const customClient = {
        chat: {
          completions: {
            create: jest.fn().mockResolvedValue({
              choices: [{ message: { content: 'Custom client response' } }],
              usage: { total_tokens: 25 }
            })
          }
        }
      };

      const result = await generateAIResponse(1, 'Hello', { client: customClient });

      expect(result.content).toBe('Custom client response');
      expect(customClient.chat.completions.create).toHaveBeenCalled();
      expect(getOpenAIClient).not.toHaveBeenCalled();
    });
  });
});
