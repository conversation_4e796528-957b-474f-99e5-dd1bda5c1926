{"name": "topic-persistent-learning", "version": "0.1.0", "private": true, "scripts": {"test": "jest --runInBand --testTimeout=30000", "test:watch": "jest --watch", "lint": "eslint . || true", "migrate": "node scripts/run_migrations_sqlite.js", "migrate:mysql": "node scripts/run_migrations.js", "migrate:dry-run": "node scripts/run_migrations_sqlite.js --dry-run", "migrate:sqlite-only": "node scripts/run_migrations_sqlite.js --sqlite-only", "migrate:manticore-only": "node scripts/run_migrations_sqlite.js --manticore-only", "migrate:demo": "node scripts/test_migrations_demo.js", "start": "node src/index.js", "start:ui": "node scripts/start-ui.js", "build:react": "node scripts/build-react.js"}, "jest": {"testEnvironment": "jsdom", "setupFiles": ["<rootDir>/tests/jest.setup.js"]}, "devDependencies": {"@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@opentelemetry/api": "^1.9.0", "@opentelemetry/exporter-trace-otlp-http": "^0.203.0", "@opentelemetry/instrumentation-express": "^0.52.0", "@opentelemetry/instrumentation-http": "^0.203.0", "@opentelemetry/sdk-node": "^0.203.0", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "babel-jest": "^30.0.5", "jest": "^29.0.0", "jest-environment-jsdom": "^30.0.5", "node-fetch": "^2.7.0", "react": "^19.1.1", "react-dom": "^19.1.1", "supertest": "^6.0.0"}, "dependencies": {"axios": "^1.4.0", "body-parser": "^2.2.0", "express": "^5.1.0", "mysql2": "^3.6.0", "openai": "^4.10.0", "sqlite": "^5.1.1", "sqlite3": "^5.1.6"}}