import React, { useState, useRef, useEffect } from 'react';
import ConversationPane from './ConversationPane.jsx';
import SummaryPane from './SummaryPane.jsx';
import LoadingSpinner from './LoadingSpinner.jsx';
import ErrorBoundary, { useErrorHandler } from './ErrorBoundary.jsx';
import TopicHeader from './TopicHeader.jsx';
import { ToastContainer, useToast } from './Toast.jsx';
import {
  safeApiCall,
  createApiCall,
  analyzeError,
  logError,
  getUserFriendlyMessage,
  createOfflineListener,
  isOnline
} from '../utils/errorHandler.js';

/**
 * App - 主应用容器
 *
 * - 将 ConversationPane 与 SummaryPane 组合为左右双栏（桌面）/单栏（移动）布局
 * - 管理 messages 与 summaries 的共享状态
 * - 实现 Summary 点击跳转到对应 message 的逻辑（通过 ConversationPane 暴露的 ref）
 *
 * 参考文件：
 * - [`src/frontend/components/ConversationPane.jsx`](src/frontend/components/ConversationPane.jsx:1)
 * - [`src/frontend/components/SummaryPane.jsx`](src/frontend/components/SummaryPane.jsx:1)
 */
export default function App() {
  // 状态管理
  const [messages, setMessages] = useState([]);
  const [summaries, setSummaries] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingText, setLoadingText] = useState('正在加载数据...');
  const [isOffline, setIsOffline] = useState(!isOnline());

  // 当前选用的 topic id（从URL参数获取，默认为1）
  const [currentTopicId, setCurrentTopicId] = useState(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const topicParam = urlParams.get('topic');
    return topicParam ? parseInt(topicParam, 10) : 1;
  });

  // 错误处理
  const { error, resetError, captureError } = useErrorHandler();

  // Toast通知管理
  const {
    toasts,
    removeToast,
    showSuccess,
    showError,
    showWarning,
    showInfo
  } = useToast();
  
  // 离线状态监听
  useEffect(() => {
    const cleanup = createOfflineListener(
      () => {
        setIsOffline(false);
        showSuccess('网络连接已恢复');
      },
      () => {
        setIsOffline(true);
        showWarning('网络连接已断开，部分功能可能无法使用');
      }
    );
    return cleanup;
  }, [showSuccess, showWarning]);

  // 在挂载时或topic_id变更时从后端拉取 conversation 与 summaries
  useEffect(() => {
    let mounted = true;
    async function loadData() {
      try {
        setIsLoading(true);
        await loadTopicData(currentTopicId);
      } catch (e) {
        console.error('加载数据失败', e);
        if (mounted) {
          const analyzedError = analyzeError(e);
          logError(analyzedError, { context: 'loadData', topicId: currentTopicId });
          captureError(analyzedError);
          showError(getUserFriendlyMessage(analyzedError), {
            showRetry: analyzedError.retryable,
            onRetry: () => loadData()
          });
        }
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    }
    loadData();
    return () => { mounted = false; };
  }, [currentTopicId, captureError, showError]);

  // ref 用于接收 ConversationPane 暴露的 scrollToMessage 函数
  // ConversationPane 支持 ref 对象：onScrollToMessage.current = scrollToMessage
  const scrollRef = useRef(null);

  // 更新URL中的topic_id参数
  function updateUrlTopicId(topicId) {
    const url = new URL(window.location);
    url.searchParams.set('topic', topicId.toString());
    window.history.replaceState({}, '', url);
  }

  // 处理topic_id变更
  async function handleTopicChange(newTopicId) {
    if (newTopicId === currentTopicId) return;

    try {
      setIsLoading(true);
      setLoadingText(`正在切换到主题 ${newTopicId}...`);

      // 更新topic_id状态
      setCurrentTopicId(newTopicId);

      // 更新URL
      updateUrlTopicId(newTopicId);

      // 清空当前数据
      setMessages([]);
      setSummaries([]);

      // 重新加载新topic的数据
      await loadTopicData(newTopicId);
    } catch (error) {
      console.error('切换主题失败:', error);
      captureError(new Error(`切换主题失败: ${error.message}`));
    } finally {
      setIsLoading(false);
    }
  }

  // 加载指定topic的数据
  async function loadTopicData(topicId) {
    // 加载对话数据
    try {
      setLoadingText('正在加载对话数据...');
      const convRes = await fetch(`/api/topics/${topicId}/conversation?limit=200`);
      if (convRes.ok) {
        const convJson = await convRes.json();
        const results = convJson.results || [];

        const newMessages = results
          .map(item => {
            const sourceData = item._source || item;
            return {
              message_id: item.id || item.message_id || sourceData.message_id || sourceData.id,
              sender: sourceData.role || item.role || 'user',
              content: item.snippet || item.content || sourceData.content || '',
              created_at: sourceData.created_at || item.created_at,
              turn_id: sourceData.turn_id || item.turn_id,
              source_type: item.source_type || 'conversation'
            };
          })
          .sort((a, b) => {
            if (a.created_at && b.created_at) {
              return new Date(a.created_at) - new Date(b.created_at);
            }
            return (a.message_id || 0) - (b.message_id || 0);
          });

        // 去重处理
        const uniqueMessages = [];
        const seenIds = new Set();
        for (const msg of newMessages) {
          if (!seenIds.has(msg.message_id)) {
            seenIds.add(msg.message_id);
            uniqueMessages.push(msg);
          }
        }

        setMessages(uniqueMessages);
      }
    } catch (e) {
      console.error('加载对话失败', e);
    }

    // 加载摘要数据
    try {
      setLoadingText('正在加载摘要数据...');
      const sumsRes = await fetch(`/api/topics/${topicId}/summaries`);
      if (sumsRes.ok) {
        const sumsJson = await sumsRes.json();
        const items = sumsJson.results || sumsJson.items || sumsJson.summaries || [];
        setSummaries(Array.isArray(items) ? items.map(s => ({
          summary_id: s.summary_id || s.id || s.message_id,
          title: s.title || s.label || `摘要 ${s.summary_id || s.id || ''}`,
          text: s.summary || s.text || s.snippet || '',
          related_message_id: (s.click_metadata && s.click_metadata.message_id) || s.message_id || s.related_message_id || null
        })) : []);
      } else {
        // 尝试备用端点
        const sumsRes2 = await fetch(`/api/topics/${topicId}/summary`);
        if (sumsRes2.ok) {
          const sumsJson2 = await sumsRes2.json();
          const items2 = sumsJson2.results || sumsJson2.items || sumsJson2 || [];
          setSummaries(Array.isArray(items2) ? items2.map(s => ({
            summary_id: s.summary_id || s.id || s.message_id,
            title: s.title || s.label || `摘要 ${s.summary_id || s.id || ''}`,
            text: s.summary || s.text || s.snippet || '',
            related_message_id: (s.click_metadata && s.click_metadata.message_id) || s.message_id || s.related_message_id || null
          })) : []);
        }
      }
    } catch (e) {
      console.error('加载摘要失败', e);
    }
  }

  // 刷新当前topic的对话和摘要
  async function handleRefreshConversation() {
    try {
      setIsLoading(true);
      setLoadingText('正在刷新数据...');
      await loadTopicData(currentTopicId);
    } catch (error) {
      console.error('刷新数据失败:', error);
      captureError(new Error(`刷新数据失败: ${error.message}`));
    } finally {
      setIsLoading(false);
    }
  }

  // 重新生成摘要
  async function handleRegenerateSummary() {
    try {
      const response = await fetch(`/api/topics/${currentTopicId}/regenerate-summary`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 重新生成成功后，刷新摘要数据
      await handleRefreshConversation();
    } catch (error) {
      console.error('重新生成摘要失败:', error);
      captureError(new Error(`重新生成摘要失败: ${error.message}`));
    }
  }

  // 从后端API获取最新的对话列表
  async function fetchMessages() {
    const fetchCall = createApiCall(`/api/topics/${currentTopicId}/conversation?limit=200`);
    const result = await safeApiCall(fetchCall);

    if (!result.success) {
      const analyzedError = analyzeError(result.error);
      logError(analyzedError, { context: 'fetchMessages', topicId: currentTopicId });
      showError(getUserFriendlyMessage(analyzedError), {
        showRetry: analyzedError.retryable,
        onRetry: () => fetchMessages()
      });
      return null;
    }

    const data = result.data;
    // 后端返回结构：{ ok: true, results: [...], meta: {...} }
    const results = data.results || [];

    // 转换为前端期望的格式并按时间排序
    const newMessages = results
      .map(item => {
        // 处理不同的数据源格式
        // 1. 来自searchContext的标准化格式：{ id, score, source_type, snippet }
        // 2. 来自SQL查询的格式：{ id, score, source_type, snippet, _source: {...} }
        const sourceData = item._source || item;
        return {
          message_id: item.id || item.message_id || sourceData.message_id || sourceData.id,
          sender: sourceData.role || item.role || 'user', // 优先从_source中获取role
          content: item.snippet || item.content || sourceData.content || '',
          created_at: sourceData.created_at || item.created_at,
          turn_id: sourceData.turn_id || item.turn_id,
          source_type: item.source_type || 'conversation'
        };
      })
      .sort((a, b) => {
        // 按created_at排序，如果没有则按message_id排序
        if (a.created_at && b.created_at) {
          return new Date(a.created_at) - new Date(b.created_at);
        }
        return (a.message_id || 0) - (b.message_id || 0);
      });

    // 去重处理：基于message_id去重
    const uniqueMessages = [];
    const seenIds = new Set();
    for (const msg of newMessages) {
      if (!seenIds.has(msg.message_id)) {
        seenIds.add(msg.message_id);
        uniqueMessages.push(msg);
      }
    }

    setMessages(uniqueMessages);
    return uniqueMessages;
  }

  // 简单的 mock AI 回复生成器（可替换为真实 AI 服务）
  // - 返回字符串作为 assistant 的回复
  // - 在测试环境中立即返回以避免测试阻塞
  async function generateMockAIResponse(userText) {
    // 在 Jest 测试环境中，立即返回固定回复以避免延迟导致超时
    if (typeof process !== 'undefined' && (process.env.JEST_WORKER_ID || process.env.NODE_ENV === 'test')) {
      if (!userText || typeof userText !== 'string' || userText.trim() === '') {
        return "（测试模式）未收到有效消息。";
      }
      return `（测试模式）这是模拟的 AI 回复： ${String(userText).trim().slice(0, 200)}`;
    }
    // 模拟一定的思考时间（例如 400-1200ms）——仅在非测试环境下生效
    const delay = 400 + Math.floor(Math.random() * 800);
    await new Promise(res => setTimeout(res, delay));
    if (!userText || typeof userText !== 'string' || userText.trim() === '') {
      return "抱歉，我没有收到有效的消息内容。可以再试一次吗？";
    }
    return `这是模拟的 AI 回复： ${userText.trim().slice(0, 200)} — 如果需要更详细的回答，请告诉我。`;
  }
  
  // 发送消息到后端API并更新本地状态（包含发送后触发 AI 自动回复的逻辑）
  async function handleSend(text) {
    // 检查网络连接
    if (isOffline) {
      showError('网络连接已断开，无法发送消息');
      return;
    }

    // 使用安全的API调用
    const sendMessageCall = createApiCall(`/api/topics/${currentTopicId}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        role: 'user',
        content: text
      })
    });

    const result = await safeApiCall(sendMessageCall);

    if (!result.success) {
      const errorMessage = getUserFriendlyMessage(result.error);
      logError(result.error, { context: 'handleSend', topicId: currentTopicId, messageText: text });
      showError(errorMessage, {
        showRetry: result.error.retryable,
        onRetry: () => handleSend(text)
      });
      throw result.error; // 重新抛出错误，让ConversationPane知道发送失败
    }

    const savedMessage = result.data;
    showSuccess('消息发送成功');

    // 用户消息发送成功后，刷新对话列表以确保显示最新内容
    try {
      await fetchMessages();
    } catch (refreshErr) {
      console.error('用户消息发送后刷新对话列表失败:', refreshErr);
      const analyzedError = analyzeError(refreshErr);
      logError(analyzedError, { context: 'fetchMessages after send', topicId: currentTopicId });
      showWarning('刷新对话列表失败，但消息已发送成功');

      // 刷新失败时，仍然添加本地消息以保证用户体验
      const newMessage = {
        message_id: savedMessage.message_id || savedMessage.id,
        sender: savedMessage.role || 'user',
        content: savedMessage.content,
        created_at: savedMessage.created_at
      };
      setMessages(prev => [...prev, newMessage]);
    }

    // ---- 开始 AI 自动回复逻辑 ----
    // 在发送成功后异步生成并发送 AI 回复，不要阻塞 handleSend 的返回（但这里我们等待以便测试/同步更新）
    (async () => {
        // 在 UI 中先添加一个 loading 占位消息（例如 sender: 'assistant', content: '' 并带有临时 id）
        const loadingId = `ai-loading-${Date.now()}`;
        const loadingMessage = {
          message_id: loadingId,
          sender: 'assistant',
          content: '',
          created_at: new Date().toISOString(),
          _isLoading: true
        };
        // 添加 loading 占位
        setMessages(prev => [...prev, loadingMessage]);
  
        try {
          // 生成 AI 回复（mock）
          const aiResponse = await generateMockAIResponse(text);
  
          // 构造 assistant 消息体并发送到后端
          const assistantBody = {
            role: 'assistant',
            content: aiResponse
          };
  
          // 模拟网络延迟：额外等待以更真实地模拟 AI 响应时间（可被测试用例 mock）
          await new Promise(res => setTimeout(res, 200));
  
          const aiResp = await fetch(`/api/topics/${currentTopicId}/messages`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(assistantBody)
          });
  
          if (!aiResp.ok) {
            // 若后端保存 AI 回复失败，记录日志并将 loading 占位替换为失败提示（但不影响已发送的用户消息）
            const errData = await aiResp.json().catch(() => ({}));
            console.error('AI 回复发送失败', errData);
            setMessages(prev => prev.map(m => m.message_id === loadingId ? {
              ...m,
              _isLoading: false,
              content: '[AI 回复发送失败]'
            } : m));
            return;
          }
  
          const savedAiMessage = await aiResp.json();
          const assistantMessage = {
            message_id: savedAiMessage.message_id || savedAiMessage.id || `ai-${Date.now()}`,
            sender: savedAiMessage.role || 'assistant',
            content: savedAiMessage.content || aiResponse,
            created_at: savedAiMessage.created_at || new Date().toISOString()
          };
  
          // 将 loading 占位替换为最终的 AI 回复（使用 message_id 匹配）
          setMessages(prev => prev.map(m => m.message_id === loadingId ? assistantMessage : m));

          // AI回复成功后，再次刷新对话列表以确保显示最新的AI回复
          try {
            await fetchMessages();
          } catch (refreshErr) {
            console.error('AI回复后刷新对话列表失败:', refreshErr);
            // 刷新失败不影响AI回复的显示，只记录错误
          }
        } catch (aiErr) {
          console.error('生成或发送 AI 回复时出错', aiErr);
          // 将 loading 占位替换为失败提示
          setMessages(prev => prev.map(m => m.message_id === loadingId ? {
            ...m,
            _isLoading: false,
            content: '[AI 回复失败]'
          } : m));

          // 即使AI回复失败，也尝试刷新对话列表以确保用户消息正确显示
          try {
            await fetchMessages();
          } catch (refreshErr) {
            console.error('AI回复失败后刷新对话列表失败:', refreshErr);
          }
        }
    })();
    // ---- 结束 AI 自动回复逻辑 ----
  }

  // 当 Summary 被选中时：尝试使用 related_message_id 或从 summary 内容推断目标 message_id
  function handleSummarySelect(summary) {
    let targetMessageId = summary.related_message_id || summary.message_id || summary.target_message_id;

    // 如果 summary 中没有直接字段，尝试从文本里查找已知 message 内容（简单匹配）
    if (!targetMessageId) {
      for (const m of messages) {
        if (summary.text && summary.text.includes(m.content)) {
          targetMessageId = m.message_id || m.id;
          break;
        }
      }
    }

    if (targetMessageId && scrollRef && scrollRef.current) {
      // scrollRef.current 应为 ConversationPane 暴露的 scrollTo(messageId) 函数
      try {
        scrollRef.current(targetMessageId, { highlight: true, behavior: 'smooth' });
      } catch (e) {
        // 兼容性回退：如果 scrollRef.current 为一个对象（ref 风格）
        if (typeof scrollRef.current === 'object' && scrollRef.current.current) {
          scrollRef.current.current(targetMessageId, { highlight: true, behavior: 'smooth' });
        }
      }
    }
  }

  // 简单响应式样式：左右两列在宽屏显示，窄屏垂直堆叠
  const containerStyle = {
    display: 'flex',
    flexDirection: 'row',
    height: '100vh',
    width: '100%',
  };

  const leftStyle = {
    flex: 1,
    minWidth: 0,
    display: 'flex',
    flexDirection: 'column'
  };

  const rightStyle = {
    width: 360,
    borderLeft: '1px solid #eee',
    overflow: 'auto',
    backgroundColor: '#fafafa'
  };

  // media query fallback: if window is narrow, stack columns
  const [isNarrow, setIsNarrow] = useState(false);
  useEffect(() => {
    function onResize() {
      setIsNarrow(window.innerWidth < 800);
    }
    onResize();
    window.addEventListener('resize', onResize);
    return () => window.removeEventListener('resize', onResize);
  }, []);
  // Global keyboard shortcuts (UX enhancements)
  // - Ctrl/Cmd+Enter: when focus is in the message input, trigger send via ConversationPane's onSend (handled locally in ConversationPane)
  // - Esc: delegate to child components (ConversationPane / SummaryPane) which already listen for Escape to clear highlights
  // We add a no-op global listener here to centralize future global shortcuts and to avoid duplicate behaviors.
  useEffect(() => {
    function onGlobalKeyDown(e) {
      // Avoid interfering with typing in inputs; child components handle their own shortcuts.
      const tag = e.target && e.target.tagName ? e.target.tagName.toUpperCase() : '';
      const editableTags = ['INPUT', 'TEXTAREA', 'SELECT'];
      if (editableTags.includes(tag)) {
        // Let the focused input handle its own shortcuts (ConversationPane handles Ctrl/Cmd+Enter and Shift+Enter)
        return;
      }

      // Example: If we want to provide a global help shortcut in future (e.g., ?), handle here.
      // For now, keep this listener lightweight.
      if (e.key === '?') {
        // prevent default to avoid browser find-in-page in some layouts
        e.preventDefault();
        // Could open a keyboard help modal in future
      }
    }

    window.addEventListener('keydown', onGlobalKeyDown, true);
    return () => window.removeEventListener('keydown', onGlobalKeyDown, true);
  }, []);

  // 如果有错误，显示错误界面
  if (error) {
    return (
      <div style={{
        height: '100vh',
        width: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          padding: 20,
          margin: 20,
          border: '1px solid #ff6b6b',
          borderRadius: 8,
          backgroundColor: '#fff5f5',
          color: '#c92a2a',
          maxWidth: 500
        }}>
          <h3 style={{ margin: '0 0 12px 0', color: '#c92a2a' }}>
            😵 应用加载失败
          </h3>
          <p style={{ margin: '0 0 16px 0', color: '#666' }}>
            {error.message || '应用遇到了问题，请尝试刷新页面。'}
          </p>
          <div style={{ display: 'flex', gap: 8 }}>
            <button
              onClick={resetError}
              style={{
                padding: '8px 16px',
                backgroundColor: '#3498db',
                color: 'white',
                border: 'none',
                borderRadius: 4,
                cursor: 'pointer',
                fontSize: 14
              }}
            >
              重试
            </button>
            <button
              onClick={() => window.location.reload()}
              style={{
                padding: '8px 16px',
                backgroundColor: '#95a5a6',
                color: 'white',
                border: 'none',
                borderRadius: 4,
                cursor: 'pointer',
                fontSize: 14
              }}
            >
              刷新页面
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 如果正在加载，显示加载界面
  if (isLoading) {
    return (
      <div style={{
        height: '100vh',
        width: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'column',
        gap: 16
      }}>
        <LoadingSpinner size={32} />
        <div style={{ color: '#666', fontSize: 16 }}>{loadingText}</div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div style={{ height: '100vh', width: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Offline Status Banner */}
        {isOffline && (
          <div style={{
            backgroundColor: '#ff6b6b',
            color: 'white',
            padding: '8px 16px',
            textAlign: 'center',
            fontSize: 14,
            fontWeight: 500
          }}>
            ⚠️ 网络连接已断开，部分功能可能无法使用
          </div>
        )}

        {/* Topic Header */}
        <TopicHeader
          currentTopicId={currentTopicId}
          onTopicChange={handleTopicChange}
          onRefreshConversation={handleRefreshConversation}
          onRegenerateSummary={handleRegenerateSummary}
          loading={isLoading}
          availableTopics={[]} // 可以后续扩展为从API获取的topic列表
        />

        {/* Main Content */}
        <div style={{ flex: 1, overflow: 'hidden' }}>
          <div style={isNarrow ? { ...containerStyle, flexDirection: 'column', height: '100%' } : { ...containerStyle, height: '100%' }}>
            <div style={leftStyle}>
              <ErrorBoundary>
                <ConversationPane
                  messages={messages}
                  onSend={handleSend}
                  onScrollToMessage={scrollRef}
                  enableLazyLoading={false}
                  autoScrollToBottom={true}
                />
              </ErrorBoundary>
            </div>

            <div style={isNarrow ? { ...rightStyle, width: '100%', borderLeft: 'none', borderTop: '1px solid #eee' } : rightStyle}>
              <ErrorBoundary>
                <SummaryPane
                  summaries={summaries}
                  onSelect={handleSummarySelect}
                />
              </ErrorBoundary>
            </div>
          </div>
        </div>

        {/* Toast Notifications */}
        <ToastContainer
          toasts={toasts}
          onRemoveToast={removeToast}
        />
      </div>
    </ErrorBoundary>
  );
}
